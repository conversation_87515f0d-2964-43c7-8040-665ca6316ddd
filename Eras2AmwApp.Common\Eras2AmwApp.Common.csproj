﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Configurations>Debug;Release;StandaloneDevelopment;Development;StandaloneRelease</Configurations>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>TRACE;DEVELOPMENT</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='StandaloneRelease|AnyCPU'">
    <DefineConstants>TRACE;STANDALONE_APP</DefineConstants>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='StandaloneDevelopment|AnyCPU'">
    <DefineConstants>DEVELOPMENT,STANDALONE_APP</DefineConstants>
    <Optimize>true</Optimize>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development|AnyCPU'">
    <DefineConstants>DEVELOPMENT</DefineConstants>
    <Optimize>true</Optimize>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Extensions\**" />
    <Compile Remove="Logger\**" />
    <EmbeddedResource Remove="Extensions\**" />
    <EmbeddedResource Remove="Logger\**" />
    <None Remove="Extensions\**" />
    <None Remove="Logger\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Serilog.Exceptions" Version="8.0.0" />
    <PackageReference Include="Serilog.Formatting.Compact" Version="3.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Serilog.Sinks.Trace" Version="3.0.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />

  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Endiancode.Utilities\Endiancode.Utilities.csproj" />
  </ItemGroup>



</Project>
