//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="RegistrationPageViewModel.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//  MAUI implementation of the RegistrationPageViewModel
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using System;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.Input;
using Eras2AmwApp.BusinessLogic.Interfaces;
using Eras2AmwApp.BusinessLogic.Models;
using Eras2AmwApp.MAUI.Interfaces;
using IServiceLocator = Eras2AmwApp.Common.Interfaces.IServiceLocator;

namespace Eras2AmwApp.MAUI.ViewModels
{
    public class RegistrationPageViewModel : EcViewModelBase
    {
        #region fields

        private readonly IEcDialogService dialogService;
        private readonly IRegistrationService registrationService;
        private readonly ICameraBarcodeReaderService barcodeReaderService;

        #endregion

        public string AppTitle { get; private set; }

        #region ctor

        public RegistrationPageViewModel(
            IServiceLocator serviceLocator,
            IEcDialogService dialogService,
            IEcNavigationService navigationService,
            IRegistrationService registrationService,
            ICameraBarcodeReaderService barcodeReaderService)
            : base(serviceLocator, navigationService)
        {
            this.dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
            this.registrationService = registrationService ?? throw new ArgumentNullException(nameof(registrationService));
            this.barcodeReaderService = barcodeReaderService ?? throw new ArgumentNullException(nameof(barcodeReaderService));

            AppTitle = registrationService.GetAppTitle();
        }

        #endregion

        #region commands


        public IRelayCommand LoadQrCodeCommand => new RelayCommand(async () => await LoadQrCodeAsync());

        public IRelayCommand LoadTestAdminCommand => new RelayCommand(async () => await LoadTestAdminAsync());
        private async Task LoadQrCodeAsync()
        {
            try
            {
                // Read the QR code using the camera
                logger.Information("Starting QR code scan process");
                var result = await ReadQrCode();

                if (string.IsNullOrEmpty(result))
                {
                    logger.Warning("QR code scan was cancelled or returned empty result");
                    return;
                }

                // Parse the QR code text
                logger.Information($"Parsing QR code text: {result}");
                string[] splittedResultText = result.Split(' ');
                if (splittedResultText.Length != 2)
                {
                    logger.Warning($"Invalid QR code format. Expected 2 parts, got {splittedResultText.Length}");
                    await ShowQrCodeError();
                    return;
                }

                // Create the customer configuration
                var config = new WebserviceCustomerConfig()
                {
                    CustomerName = splittedResultText[0],
                    Url = splittedResultText[1]
                };
                logger.Information($"QR code parsed successfully. Customer: {config.CustomerName}, URL: {config.Url}");

                // Update the webservice customer
                logger.Information("Updating webservice customer...");
                registrationService.UpdateWebserviceCustomer(config);
                logger.Information("Webservice customer updated successfully");

                // Navigate to the login page
                await navigationService.NavigateToAsync<LoginPageViewModel>();
                await navigationService.RemoveBackStackAsync();
                logger.Information("Navigation to login page completed");
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occurred while attempting to load QR code data!");
                await dialogService.AcceptAsync("Error loading QR code: " + e.Message, "OK", "Error");
                throw;
            }
            finally
            {
                navigationService.ShowInitPageOnResume = true;
            }
        }

        private async Task LoadTestAdminAsync()
        {
            try
            {
                var config = new WebserviceCustomerConfig()
                {
                    CustomerName = "AdminTest",
                    Url = "AdminTest"
                };

                registrationService.UpdateWebserviceCustomer(config);

                await navigationService.NavigateToAsync<LoginPageViewModel>();
                await navigationService.RemoveBackStackAsync();
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to load data for TestAdmin!");
                throw;
            }
            finally
            {
                navigationService.ShowInitPageOnResume = true;
            }
        }

        #endregion

        #region private methods

        private async Task ShowQrCodeError()
        {
            await dialogService.AcceptAsync("Der QrCode hat das falsche Format", "OK", "Fehler");
        }

        private async Task<string> ReadQrCode()
        {
            try
            {
                navigationService.ShowInitPageOnResume = false;

                // Use the barcode reader service to scan a QR code
                logger.Information("Starting QR code scan");
                string result = await barcodeReaderService.ScanBarcodeAsync();

                if (string.IsNullOrEmpty(result))
                {
                    logger.Warning("QR code scan returned empty result");
                    await dialogService.AcceptAsync("QR code scan was cancelled or returned empty result.", "OK", "Information");
                }
                else
                {
                    logger.Information($"QR code scan successful: {result}");
                }

                return result;
            }
            catch (Exception ex)
            {
                logger.Error(ex, "Error scanning QR code");
                await dialogService.AcceptAsync("Error scanning QR code: " + ex.Message, "OK", "Error");
                return string.Empty;
            }
        }

        #endregion
    }
}
