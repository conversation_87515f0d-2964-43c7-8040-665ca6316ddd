//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="SimpleLogger.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Common.Implementations
{
    using System;

    using Eras2AmwApp.Common.Ioc;

    using Microsoft.Extensions.Logging;

    public class SimpleLogger : ILogger
    {
        private readonly string name;

        private readonly Func<string, LogLevel, bool> filter;

        private readonly Serilog.ILogger logger;

        public SimpleLogger(string name, Func<string, LogLevel, bool> filter, Serilog.ILogger serilogLogger)
        {
            this.name = name;
            this.filter = filter;
            logger = serilogLogger ?? throw new ArgumentNullException(nameof(serilogLogger));
        }
        
        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception exception, Func<TState, Exception, string> formatter)
        {
            if (!IsEnabled(logLevel))
            {
                return;
            }

            if (!filter(name, logLevel))
            {
                return;
            }

            var message = $"{name} {logLevel}: {formatter(state, exception)}";

            switch (logLevel)
            {
                case LogLevel.Error:
                case LogLevel.Critical:
                logger.Error(exception, message);
                    break;
                case LogLevel.Information:
                case LogLevel.Trace:
                    logger.Information(message);
                    break;
                case LogLevel.Warning:
                    logger.Warning(message);
                    break;
                default:
                    break;
            }
        }

        public bool IsEnabled(LogLevel logLevel) => true;

        public IDisposable BeginScope<TState>(TState state) => null;
    }
}