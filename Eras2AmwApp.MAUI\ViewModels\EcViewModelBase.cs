using System.Reflection;
using System.Text.RegularExpressions;
using AutoMapper;
using CommunityToolkit.Mvvm.ComponentModel;
using Eras2AmwApp.Common.Interfaces;
using Eras2AmwApp.MAUI.Interfaces;
using FluentValidation;
using FluentValidation.Results;
using Serilog;
using IServiceLocator = Eras2AmwApp.Common.Interfaces.IServiceLocator;

namespace Eras2AmwApp.MAUI.ViewModels
{
    public abstract partial class EcViewModelBase : BaseViewModel
    {
        protected readonly IEcNavigationService navigationService;
        protected readonly ILogger logger;
        // Localization service removed
        protected readonly IServiceLocator serviceLocator;
        protected readonly IAppSettings appSettings;
        protected readonly IMapper mapper;

        [ObservableProperty]
        private bool _isSetupDone;

        protected EcViewModelBase(IServiceLocator serviceLocator, IEcNavigationService navigationService)
        {
            this.serviceLocator = serviceLocator ?? throw new ArgumentNullException(nameof(serviceLocator));

            logger = serviceLocator.Logger;
            // Localization service removed
            appSettings = serviceLocator.AppSettings;
            mapper = serviceLocator.Mapper;

            this.navigationService = navigationService ?? throw new ArgumentNullException(nameof(navigationService));

            ValidatorOptions.Global.DefaultClassLevelCascadeMode = CascadeMode.Stop;
            ValidatorOptions.Global.DefaultRuleLevelCascadeMode = CascadeMode.Stop;
        }

        public virtual Task SetupAsync(object navigationData)
        {
            IsSetupDone = true;
            return Task.CompletedTask;
        }

        protected void UpdateErrorMessages(ValidationResult result)
        {
            ArgumentNullException.ThrowIfNull(result, nameof(result));

            ClearErrorMessages();

            if (result.IsValid)
            {
                return;
            }

            foreach (ValidationFailure error in result.Errors)
            {
                var properyName = error.PropertyName;
                var message = error.ErrorMessage;

                PropertyInfo? prop = GetType().GetProperty($"{properyName}ErrorText");
                prop?.SetValue(this, message);

                PropertyInfo? prop2 = GetType().GetProperty($"{properyName}HasError");
                prop2?.SetValue(this, true);
            }
        }

        private void ClearErrorMessages()
        {
            IEnumerable<PropertyInfo> errorProperties = GetType().GetProperties().Where(x => x.Name.EndsWith("HasError"));

            foreach (PropertyInfo errorProperty in errorProperties)
            {
                errorProperty.SetValue(this, false);
            }
        }

        [GeneratedRegex(@"[^\u0000-\u007F\u00E4\u00F6\u00FC\u00C4\u00D6\u00DC\u00DF]+")]
        private static partial Regex UnsupportedCharactersRegex();

        protected static string RemoveUnsupportedCharacters(string inputString)
        {
            return UnsupportedCharactersRegex().Replace(inputString, string.Empty);
        }
    }
}

