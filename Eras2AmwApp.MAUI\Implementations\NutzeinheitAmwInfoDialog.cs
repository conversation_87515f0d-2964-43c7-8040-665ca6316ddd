//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzeinheitAmwInfoDialog.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//  Dialog for selecting AMW info keys for Nutzeinheit
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.MAUI.Implementations
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Eras2AmwApp.MAUI.Interfaces;
    using Eras2AmwApp.MAUI.Models;

    public class NutzeinheitAmwInfoDialog : INutzeinheitAmwInfoDialog
    {
        #region fields

        private readonly IStammdatenService stammdatenService;
        // Localization service removed
        private readonly INutzeinheitService nutzeinheitService;
        private readonly IEcDialogService dialogService;

        private List<AmwInfoKey>? infoKeyList;
        private NutzeinheitOrderState? nutzeinheitOrderState;

        #endregion

        #region ctor

        public NutzeinheitAmwInfoDialog(
            IStammdatenService stammdatenService,
            INutzeinheitService nutzeinheitService,
            IEcDialogService dialogService)
        {
            this.stammdatenService = stammdatenService ?? throw new ArgumentNullException(nameof(stammdatenService));
            // Localization service removed
            this.nutzeinheitService = nutzeinheitService ?? throw new ArgumentNullException(nameof(nutzeinheitService));
            this.dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
        }

        #endregion

        #region public methods

        public async Task<NutzeinheitOrderState> ShowNutzeinheitAmwInfoDialog(NutzeinheitVM nutzeinheitVM)
        {
            // Get the nutzeinheit order state
            nutzeinheitOrderState = nutzeinheitService.GetNutzeinheitOrderState(nutzeinheitVM.NutzeinheitOrder);

            // Get the info keys
            infoKeyList = stammdatenService.GetNutzeinheitInfoKeys();

            // Add an option to delete the info key
            var emptyKey = new AmwInfoKey
            {
                Key = -1,
                Info = "<Infoschlüssel löschen>"
            };
            infoKeyList.Insert(0, emptyKey);

            // Find the currently selected key
            string selectedKeyInfo = infoKeyList[0].Info;
            AmwInfoKey? selectedKey = infoKeyList.FirstOrDefault(x => x.Guid == nutzeinheitOrderState?.AmwInfoKeyGuid);
            if (selectedKey != null)
            {
                selectedKeyInfo = selectedKey.Info;
            }

            // Show a dialog to select an info key
            string title = "Infoschlüssel Nutzeinheit";
            string message = "Bitte wählen Sie einen Infoschlüssel für die Nutzeinheit aus.";

            // Since we don't have a direct way to show a selection dialog with IEcDialogService,
            // we'll use a simple accept/decline dialog for each option until the user selects one

            // First show the warning message
            await dialogService.AcceptAsync(message, "OK", title);

            // Then iterate through the options
            foreach (var infoKey in infoKeyList)
            {
                string optionMessage = $"Möchten Sie den Infoschlüssel '{infoKey.Info}' auswählen?";
                var response = await dialogService.AcceptDeclineAsync(
                    optionMessage,
                    "Ja",
                    "Nein",
                    title);

                if (response == DialogResponse.Accept)
                {
                    // User selected this option
                    if (infoKey.Key == -1)
                    {
                        // User selected the "delete" option
                        nutzeinheitOrderState.AmwInfoKeyGuid = null;
                    }
                    else
                    {
                        // User selected an info key
                        nutzeinheitOrderState.AmwInfoKeyGuid = infoKey.Guid;
                    }

                    // Break out of the loop since we've made a selection
                    break;
                }
            }

            return nutzeinheitOrderState;
        }

        #endregion
    }
}
