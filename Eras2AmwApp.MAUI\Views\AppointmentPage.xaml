<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://schemas.microsoft.com/dotnet/2021/maui/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:behaviors="clr-namespace:Eras2AmwApp.MAUI.Behaviors"
             xmlns:syncfusion="clr-namespace:Syncfusion.Maui.Calendar;assembly=Syncfusion.Maui.Calendar"
             xmlns:tabView="clr-namespace:Syncfusion.Maui.TabView;assembly=Syncfusion.Maui.TabView"
             xmlns:converter="clr-namespace:Eras2AmwApp.MAUI.Converters"
             xmlns:buttons="clr-namespace:Syncfusion.Maui.Buttons;assembly=Syncfusion.Maui.Buttons"
             xmlns:inputLayout="clr-namespace:Syncfusion.Maui.Core;assembly=Syncfusion.Maui.Core"

             xmlns:syncfusionGrid="clr-namespace:Syncfusion.Maui.DataGrid;assembly=Syncfusion.Maui.DataGrid"
             xmlns:syncfusionTree="clr-namespace:Syncfusion.Maui.TreeView;assembly=Syncfusion.Maui.TreeView"
             xmlns:sfPager="clr-namespace:Syncfusion.Maui.DataGrid.DataPager;assembly=Syncfusion.Maui.DataGrid"
             mc:Ignorable="d"
             x:Class="Eras2AmwApp.MAUI.Views.AppointmentPage">

    <ContentPage.Resources>
        <converter:OrderStateConverter x:Key="OrderStateConverter"></converter:OrderStateConverter>
        <converter:ShortTimeConverter x:Key="ShortTimeConverter"></converter:ShortTimeConverter>
        <converter:DataGridTimeFormatConverter x:Key="DataGridTimeFormatConverter"></converter:DataGridTimeFormatConverter>
        <converter:DeviceNutzeinheitOrderConverter x:Key="DeviceNutzeinheitOrderConverter"></converter:DeviceNutzeinheitOrderConverter>

        <!-- Styles for DataGrid -->
        <Style x:Key="HeaderStyle" TargetType="syncfusionGrid:DataGridHeaderCell">
            <Setter Property="FontSize" Value="10" />
            <Setter Property="FontAttributes" Value="Bold" />
        </Style>

        <Style x:Key="CellStyle" TargetType="syncfusionGrid:DataGridCell">
            <Setter Property="FontSize" Value="10" />
        </Style>
    </ContentPage.Resources>

    <ContentPage.Behaviors>
        <behaviors:EventToCommandBehavior EventName="Appearing" Command="{Binding AppearingCommand}" />
        <behaviors:EventToCommandBehavior EventName="Disappearing" Command="{Binding DisappearingCommand}" />
    </ContentPage.Behaviors>

    <tabView:SfTabView EnableSwiping="False">

        <tabView:SfTabItem Header="Kalender">

            <tabView:SfTabItem.Content>

                <Grid>

                    <Grid.RowDefinitions>
                        <RowDefinition Height="35*"></RowDefinition>
                        <RowDefinition Height="65*"></RowDefinition>
                    </Grid.RowDefinitions>

                    <syncfusion:SfCalendar  Grid.Row="0"
                                            IsVisible="{Binding ShowCalendar}"
                                            ShowInlineEvents="False"
                                            SelectionMode="SingleSelection"
                                            ShowLeadingAndTrailingDays="True"
                                            NavigationDirection="Horizontal"
                                            FirstDayofWeek="1"
                                            SelectedDate="{Binding SelectedDate, Mode=TwoWay}"
                                            DataSource="{Binding CalendarInlineEvents}"
                                            ViewMode="MonthView"
                                            ShowYearView="False"
                                            MaximumEventIndicatorCount="1">
                    </syncfusion:SfCalendar>

                    <Grid Grid.Row="1">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <StackLayout Grid.Row="0"
                                     Orientation="Horizontal"
                                     Spacing="0"
                                     HorizontalOptions="Fill">

                            <Label  Text="{Binding NavigationHeader}"
                                    Margin="5,0,0,0"
                                    FontSize="Large" />

                            <Image  x:Name="TerminInfo"
                                    Margin="5,0,5,0"
                                    Source="infoIcon.png"
                                    HeightRequest="30"
                                    WidthRequest="30"
                                    VerticalOptions="Center"
                                    HorizontalOptions="End">

                                <Image.GestureRecognizers>
                                    <TapGestureRecognizer   Command="{Binding ShowTerminInfoCommand}"
                                                            NumberOfTapsRequired="1" />
                                </Image.GestureRecognizers>
                            </Image>
                        </StackLayout>

                        <CollectionView   x:Name="AppointmentList"
                                          Margin="10,2,10,2"
                                          Grid.Row="1"
                                          ItemsSource="{Binding AppointmentsList}"
                                          SelectedItem="{Binding SelectedListAppointment}"
                                          SelectionMode="Single"
                                          SelectionChanged="OnAppointmentSelected">

                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <Frame  Margin="2"
                                            Padding="0"
                                            BorderColor="LightGray"
                                            CornerRadius="0"
                                            HasShadow="True">

                                        <Grid RowSpacing="0" ColumnSpacing="0">

                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"></RowDefinition>
                                                <RowDefinition Height="Auto"></RowDefinition>
                                                <RowDefinition Height="Auto"></RowDefinition>
                                            </Grid.RowDefinitions>

                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="6"></ColumnDefinition>
                                                <ColumnDefinition Width="9"></ColumnDefinition>
                                                <ColumnDefinition Width="20*"></ColumnDefinition>
                                                <ColumnDefinition Width="55*"></ColumnDefinition>
                                                <ColumnDefinition Width="45"></ColumnDefinition>
                                                <ColumnDefinition Width="50"></ColumnDefinition>
                                            </Grid.ColumnDefinitions>

                                            <Label  x:Name="StatusColor"
                                                    Grid.Row="0"
                                                    Grid.RowSpan="3"
                                                    Grid.Column="0"
                                                    BackgroundColor="{Binding OrderState, Converter={StaticResource OrderStateConverter}}">
                                            </Label>

                                            <Label  x:Name="DeviceColor"
                                                    Grid.Row="0"
                                                    Grid.RowSpan="3"
                                                    Grid.Column="1"
                                                    Margin="3,0,0,0"
                                                    BackgroundColor="{Binding NutzeinheitenState, Converter={StaticResource DeviceNutzeinheitOrderConverter}}">
                                            </Label>

                                            <Label x:Name="StartTime"
                                                   Grid.Row="0"
                                                   Grid.Column="2"
                                                   Margin="5,5,5,5"
                                                   Text="{Binding StartTime, Converter={StaticResource ShortTimeConverter}}"
                                                   TextColor="Black"
                                                   FontSize="Small"
                                                   FontAttributes="Bold">
                                            </Label>

                                            <Label x:Name="EndTime"
                                                   Grid.Row="1"
                                                   Grid.Column="2"
                                                   Margin="5,5,5,5"
                                                   Text="{Binding EndTime, Converter={StaticResource ShortTimeConverter}}"
                                                   TextColor="Black"
                                                   FontSize="Small"
                                                   FontAttributes="Bold">
                                            </Label>

                                            <Label x:Name="MainText"
                                                   Grid.Row="0"
                                                   Grid.Column="3"
                                                   Margin="5,5,30,0"
                                                   Text="{Binding Address}"
                                                   TextColor="Black"
                                                   FontSize="Small"
                                                   FontAttributes="Bold">
                                            </Label>

                                            <Label x:Name="MinorText"
                                                   Grid.Row="1"
                                                   Grid.Column="3"
                                                   Margin="5,0,30,5"
                                                   Text="{Binding OrderLabel}"
                                                   FontSize="12">
                                            </Label>

                                            <Label  x:Name="OrderNumber"
                                                    Grid.Row="0"
                                                    Grid.Column="4"
                                                    Grid.ColumnSpan="2"
                                                    HorizontalTextAlignment="End"
                                                    Margin="0,5,5,0"
                                                    Text="{Binding OrderNumber}"
                                                    FontSize="Small"
                                                    FontAttributes="Bold">
                                            </Label>

                                            <Image  x:Name="Icon1"
                                                    Grid.Row="1"
                                                    Grid.Column="4"
                                                    Margin="0"
                                                    Source="infoIcon.png"
                                                    WidthRequest="30"
                                                    HeightRequest="30"
                                                    VerticalOptions="Center"
                                                    HorizontalOptions="Center"
                                                    IsEnabled="{Binding HasNote}"
                                                    IsVisible="{Binding HasNote}" >

                                                <Image.GestureRecognizers>
                                                    <TapGestureRecognizer   Command="{Binding Path=BindingContext.IconButtonCommand, Source={x:Reference AppointmentList}}"
                                                                            CommandParameter="{Binding Guid}"
                                                                            NumberOfTapsRequired="1" />
                                                </Image.GestureRecognizers>
                                            </Image>

                                            <Image  Grid.Row="1"
                                                    Grid.Column="5"
                                                    Margin="0"
                                                    Source="nelist.png"
                                                    WidthRequest="40"
                                                    HeightRequest="40"
                                                    VerticalOptions="Start"
                                                    HorizontalOptions="End" >

                                                <Image.GestureRecognizers>
                                                    <TapGestureRecognizer   Command="{Binding Path=BindingContext.NeListCommand, Source={x:Reference AppointmentList}}"
                                                                            CommandParameter="{Binding Guid}"
                                                                            NumberOfTapsRequired="1" />
                                                </Image.GestureRecognizers>
                                            </Image>
                                        </Grid>
                                    </Frame>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                    </Grid>

                </Grid>

            </tabView:SfTabItem.Content>

        </tabView:SfTabItem>

        <tabView:SfTabItem Header="Aufträge Liste">

            <tabView:SfTabItem.Content>

                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="50*"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                    </Grid.RowDefinitions>

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="50*"></ColumnDefinition>
                        <ColumnDefinition Width="50*"></ColumnDefinition>
                    </Grid.ColumnDefinitions>

                    <SearchBar  x:Name="filterText"
                                        Grid.Row="0"
                                        Grid.Column="0"
                                        IsVisible="true"
                                        Placeholder="Suche"
                                        Text="{Binding SearchbarText}">

                    </SearchBar>

                    <StackLayout Grid.Row="0"
                                         Grid.Column="1"
                                         Orientation="Horizontal">

                        <Label  Text="Gruppieren:"
                                        VerticalOptions="Center"
                                        FontSize="Medium">

                        </Label>

                        <Picker Title="Wähle eine Option:"
                                        HorizontalOptions="Fill"
                                        ItemsSource="{Binding GroupOptionList}"
                                        SelectedItem="{Binding SelectedGroupOption}"
                                        FontSize="Medium">

                        </Picker>

                    </StackLayout>

                    <Label  x:Name="TermineCount"
                                    Grid.Row="1"
                                    Grid.Column="0"
                                    Grid.ColumnSpan="2"
                                    Text="{Binding TerminCount}"
                                    Margin="5,0,5,0"
                                    FontSize="Medium"/>

                    <syncfusionGrid:SfDataGrid  x:Name="appointmentDataGrid"
                                                Grid.Row="2"
                                                Grid.Column="0"
                                                Grid.ColumnSpan="2"
                                                AutoGenerateColumnsMode="None"
                                                SelectionMode="Single"
                                                NavigationMode="Row"
                                                GroupCaptionTextFormat="{}{ColumnName}: {Key}">

                        <syncfusionGrid:SfDataGrid.Columns>

                            <syncfusionGrid:DataGridTemplateColumn MappingName="OrderState" HeaderText="S" Width="5">
                                <syncfusionGrid:DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Label BackgroundColor="{Binding OrderState, Converter={StaticResource OrderStateConverter}}"/>
                                    </DataTemplate>
                                </syncfusionGrid:DataGridTemplateColumn.CellTemplate>
                            </syncfusionGrid:DataGridTemplateColumn>

                            <syncfusionGrid:DataGridTextColumn  HeaderText="Nummer"
                                                                HeaderStyle="{StaticResource HeaderStyle}"
                                                                CellStyle="{StaticResource CellStyle}"
                                                                Width="60"
                                                                MappingName="OrderNumber" />

                            <syncfusionGrid:DataGridTextColumn  HeaderText="Startzeit"
                                                                HeaderStyle="{StaticResource HeaderStyle}"
                                                                CellStyle="{StaticResource CellStyle}"
                                                                Width="100"
                                                                MappingName="StartTime"
                                                                DisplayBinding="{Binding StartTime, Converter={StaticResource DataGridTimeFormatConverter}}"/>

                            <syncfusionGrid:DataGridTextColumn  HeaderText="Endzeit"
                                                                HeaderStyle="{StaticResource HeaderStyle}"
                                                                CellStyle="{StaticResource CellStyle}"
                                                                Width="100"
                                                                MappingName="EndTime"
                                                                DisplayBinding="{Binding EndTime, Converter={StaticResource DataGridTimeFormatConverter}}"/>

                            <syncfusionGrid:DataGridTextColumn  HeaderText="Datum"
                                                                HeaderStyle="{StaticResource HeaderStyle}"
                                                                CellStyle="{StaticResource CellStyle}"
                                                                MappingName="DateTime">
                            </syncfusionGrid:DataGridTextColumn>

                            <syncfusionGrid:DataGridTextColumn  HeaderText="Adresse"
                                                                HeaderStyle="{StaticResource HeaderStyle}"
                                                                CellStyle="{StaticResource CellStyle}"
                                                                MappingName="Address" />

                        </syncfusionGrid:SfDataGrid.Columns>

                    </syncfusionGrid:SfDataGrid>

                    <sfPager:SfDataPager    x:Name ="dataPager"
                                            Grid.Row="3"
                                            Grid.Column="0"
                                            Grid.ColumnSpan="2"
                                            PageCount="1"
                                            PageSize="20"
                                            HeightRequest ="50"
                                            NumericButtonCount="20"
                                            Source="{Binding DataGridAppointmentList}">
                    </sfPager:SfDataPager>

                </Grid>
            </tabView:SfTabItem.Content>

        </tabView:SfTabItem>

        <tabView:SfTabItem Header="Nachtermine">
            <tabView:SfTabItem.Content>

                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                        </Grid.RowDefinitions>

                        <Label  Text="Nachtermine"
                                Grid.Row="0"
                                HorizontalOptions="Center"
                                Margin="5,5,5,10"
                                FontSize="Large" />

                        <syncfusionTree:SfTreeView  Grid.Row="1"
                                                    x:Name="treeView"
                                                    ItemsSource="{Binding TechnicianAppointmentVMs}"
                                                    ChildPropertyName="AppointmentTermine"
                                                    AutoExpandMode="None"
                                                    Indentation="20"
                                                    TapCommand="{Binding TreeViewItemTappedCommand}">

                            <syncfusionTree:SfTreeView.ItemTemplate>
                                <DataTemplate>
                                    <Grid Padding="5,5,5,0">

                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"></RowDefinition>
                                        </Grid.RowDefinitions>

                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="70*"></ColumnDefinition>
                                            <ColumnDefinition Width="30*"></ColumnDefinition>
                                        </Grid.ColumnDefinitions>

                                        <Label  Grid.Column="0"
                                                Text="{Binding FileName}"
                                                VerticalTextAlignment="Center"/>

                                        <Label  Grid.Column="1"
                                                Text="{Binding NutzerList.Count, StringFormat='({0} Nutzer)'}"
                                                HorizontalTextAlignment="End"/>

                                    </Grid>
                                </DataTemplate>
                            </syncfusionTree:SfTreeView.ItemTemplate>
                        </syncfusionTree:SfTreeView>
                    </Grid>

            </tabView:SfTabItem.Content>
        </tabView:SfTabItem>

        <tabView:SfTabItem Header="Einstellungen">
            <tabView:SfTabItem.Content>
                <ScrollView>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="20"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="20"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="20"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="5*"></ColumnDefinition>
                            <ColumnDefinition Width="45*"></ColumnDefinition>
                            <ColumnDefinition Width="45*"></ColumnDefinition>
                            <ColumnDefinition Width="5*"></ColumnDefinition>
                        </Grid.ColumnDefinitions>

                        <Label Grid.Row="0"
                               Grid.Column="1"
                               Grid.ColumnSpan="2"
                               Text="Geräteinformationen"
                               FontSize="Large"
                               FontAttributes="Bold"
                               HorizontalOptions="Center">
                        </Label>

                        <Label  Grid.Row="2"
                                Grid.Column="1"
                                Text="Hersteller"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="2"
                                Grid.Column="2"
                                Text="{Binding DeviceManufacturer}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="3"
                                Grid.Column="1"
                                Text="Gerätemodell"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="3"
                                Grid.Column="2"
                                Text="{Binding DeviceModel}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="4"
                                Grid.Column="1"
                                Text="OS"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="4"
                                Grid.Column="2"
                                Text="{Binding DevicePlatform}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="5"
                                Grid.Column="1"
                                Text="OS Version"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="5"
                                Grid.Column="2"
                                Text="{Binding DevicePlatformVersion}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="6"
                                Grid.Column="1"
                                Text="App Version"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="6"
                                Grid.Column="2"
                                Text="{Binding AppVersion}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="7"
                                Grid.Column="1"
                                Text="DB Version"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="7"
                                Grid.Column="2"
                                Text="{Binding DatabaseVersion}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="8"
                                Grid.Column="1"
                                Text="Internet"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="8"
                                Grid.Column="2"
                                Text="{Binding InternetAvailableInfo}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label Grid.Row="10"
                               Grid.Column="1"
                               Grid.ColumnSpan="2"
                               Text="Benutzerinformationen"
                               FontSize="Large"
                               FontAttributes="Bold"
                               HorizontalOptions="Center">
                        </Label>

                        <Label  Grid.Row="12"
                                Grid.Column="1"
                                Text="Live-Synchronisierung"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <CheckBox   x:Name="liveSyncCheckbox"
                                    HorizontalOptions="Start"
                                    VerticalOptions="Center"
                                    Color="Black"
                                    IsChecked="{Binding IsLiveSyncOn}"
                                    IsEnabled="{Binding IsAdminTestUser}"
                                    IsVisible="{Binding IsAdminTestUser}"
                                    Grid.Row="12"
                                    Grid.Column="2">

                        </CheckBox>

                        <Label  Grid.Row="13"
                                Grid.Column="1"
                                Text="Benutzername"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="13"
                                Grid.Column="2"
                                Text="{Binding Username}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="14"
                                Grid.Column="1"
                                Text="Kundenname"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="14"
                                Grid.Column="2"
                                Text="{Binding Customername}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="15"
                                Grid.Column="1"
                                Text="Webservice URL"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <Label  Grid.Row="15"
                                Grid.Column="2"
                                Text="{Binding WebserviceUrl}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <!--<Label  Grid.Row="15"
                                Grid.Column="1"
                                Text="{markupExtensions:Localisation UserEmail}"
                                FontSize="Medium"
                                HorizontalOptions="Start">
                        </Label>

                        <inputLayout:SfTextInputLayout  Padding="5"
                                                        Margin="5"
                                                        BackgroundColor="White"
                                                        ContainerType="Outlined"
                                                        Grid.Row="15"
                                                        Grid.Column="2"
                                                        VerticalOptions="Center"
                                                        Hint="{Binding UserEmailErrorText}"
                                                        HasError="{Binding UserEmailHasError}">

                            <Entry  Text="{Binding UserEmail}"
                                    FontSize="Medium"
                                    VerticalOptions="StartAndExpand">

                                <Entry.Behaviors>
                                    <behaviors:EventToCommandBehavior   EventName="Unfocused"
                                                                        Command="{Binding UpdateUserEntryCommand}">
                                    </behaviors:EventToCommandBehavior>
                                </Entry.Behaviors>
                            </Entry>

                        </inputLayout:SfTextInputLayout>-->

                        <buttons:SfButton   Margin="5,5,5,0"
                                            Text="Lokales Backup"
                                            Command="{Binding LocalBackupCommand}"
                                            Grid.Row="16" Grid.Column="1"
                                            VerticalOptions="Center"
                                            IsEnabled="{Binding IsAdminTestUser}"
                                            IsVisible="{Binding IsAdminTestUser}"
                                            BackgroundColor="#3498db"
                                            TextColor="White">
                        </buttons:SfButton>

                        <buttons:SfButton   Margin="5,5,5,0"
                                            Text="Remote Backup"
                                            Command="{Binding RemoteBackupCommand}"
                                            Grid.Row="16" Grid.Column="2"
                                            VerticalOptions="Center"
                                            IsEnabled="{Binding IsAdminTestUser}"
                                            IsVisible="{Binding IsAdminTestUser}"
                                            BackgroundColor="#3498db"
                                            TextColor="White"/>

                        <buttons:SfButton   Margin="5,5,5,0"
                                            Text="Synchronisieren"
                                            Command="{Binding SyncOrdersCommand}"
                                            Grid.Row="18" Grid.Column="1"
                                            VerticalOptions="Center"
                                            IsEnabled="{Binding IsAdminTestUser}"
                                            IsVisible="{Binding IsAdminTestUser}"
                                            BackgroundColor="#3498db"
                                            TextColor="White"/>

                        <buttons:SfButton   Margin="5,5,5,0"
                                            Text="DB Löschen"
                                            Command="{Binding DeleteAmwDatabaseCommand}"
                                            Grid.Row="17" Grid.Column="1"
                                            VerticalOptions="Center"
                                            IsEnabled="{Binding IsAdminTestUser}"
                                            IsVisible="{Binding IsAdminTestUser}"
                                            BackgroundColor="#3498db"
                                            TextColor="White"/>

                        <buttons:SfButton   Margin="5,5,5,0"
                                            Text="Werksreset"
                                            Command="{Binding FactoryResetCommand}"
                                            Grid.Row="17" Grid.Column="2"
                                            VerticalOptions="Center"
                                            IsEnabled="{Binding IsAdminTestUser}"
                                            IsVisible="{Binding IsAdminTestUser}"
                                            BackgroundColor="#3498db"
                                            TextColor="White"/>

                    </Grid>
                </ScrollView>
            </tabView:SfTabItem.Content>
        </tabView:SfTabItem>

    </tabView:SfTabView>

</ContentPage>